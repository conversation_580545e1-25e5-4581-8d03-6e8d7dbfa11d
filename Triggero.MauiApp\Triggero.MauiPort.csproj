<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net9.0-android;net9.0-ios</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net9.0-tizen</TargetFrameworks> -->

		<!-- MacCatalyst support explicitly excluded from this port -->

		<OutputType>Exe</OutputType>
		<RootNamespace>Triggero.MobileMaui</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Display name -->
		<ApplicationTitle>Triggero.MobileMaui</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.companyname.triggero.mobilemaui</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>
    <Version>1</Version>

  </PropertyGroup>

  <PropertyGroup>
    <WindowsPackageType>MSIX</WindowsPackageType>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.2</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</SupportedOSPlatformVersion>
    <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</TargetPlatformMinVersion>
  </PropertyGroup>

	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>


	<ItemGroup>
	  <MauiAsset Remove="Resources\raw\Images\breath.jpg" />
	  <MauiAsset Remove="Resources\raw\Images\linkout.svg" />
	  <MauiAsset Remove="Resources\raw\Lottie\breath.json" />
	  <MauiAsset Remove="Resources\raw\Lottie\chatbot.json" />
	  <MauiAsset Remove="Resources\raw\Lottie\home.json" />
	  <MauiAsset Remove="Resources\raw\Lottie\library.json" />
	  <MauiAsset Remove="Resources\raw\Lottie\plus.json" />
	  <MauiAsset Remove="Resources\raw\Lottie\tests.json" />
	  <MauiAsset Remove="Resources\raw\Privacy\ru.html" />
	</ItemGroup>

	<ItemGroup>
	  <MauiFont Remove="Resources\Fonts\OpenSans-Bold.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-BoldItalic.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-ExtraBold.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-ExtraBoldItalic.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-Italic.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-Light.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-LightItalic.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-Medium.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-MediumItalic.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-Regular.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-Semibold.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-SemiBoldItalic.ttf" />
	</ItemGroup>

	<ItemGroup>
		<!--<PackageReference Include="AppoMobi.Maui.DrawnUi" Version="1.5.1.4" />-->
		<!--<PackageReference Include="Microsoft.Maui.Controls" Version="$(MauiVersion)" />-->
    <PackageReference Include="CommunityToolkit.Maui" Version="12.0.0" />
    <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.80" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />
    <PackageReference Include="Sharpnado.MaterialFrame.Maui" Version="2.0.0" />
	</ItemGroup>

  <ItemGroup>
    <PackageReference Include="Syncfusion.Maui.Buttons" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Calendar" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Carousel" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Charts" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Gauges" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Picker" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.ProgressBar" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Sliders" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Rotator" Version="30.1.37" />

    <PackageReference Include="Plugin.Maui.Audio" Version="4.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Common\Triggero.Common.csproj" />
    <ProjectReference Include="..\Triggero.Web\MobileAPIWrapper\MobileAPIWrapper.csproj" />
    <ProjectReference Include="..\Triggero.Web\Triggero.ChatBot\Triggero.ChatBot.csproj" />
    <ProjectReference Include="..\Triggero.Web\Triggero.Models\Triggero.Models.csproj" />
    <ProjectReference Include="..\Triggero.Web\TriggeroWeb.Models\Triggero.Domain.csproj" />
    <ProjectReference Include="..\..\DrawnUi.Maui\src\Maui\DrawnUi\DrawnUi.Maui.csproj" />
  </ItemGroup>

  <ItemGroup>
    <MauiXaml Update="MainPage.xaml">
      <Generator>MSBuild:Compile</Generator>
    </MauiXaml>
  </ItemGroup>
   

</Project>
