﻿using AppoMobi.Maui.Gestures;
using AppoMobi.Specials;
 
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.ViewModels;
using Triggero.MauiMobileApp.Views.Drawn;
using Triggero.MauiMobileApp.Views.Pages;



namespace Triggero.MauiMobileApp.Views
{

    public partial class LibraryView : ContentView
    {
        private DrawnListCategories _exercises;
        private DrawnListCategories _practices;
        private DrawnListCategories _topics;
        private DrawnBreathTab _breath;

        public LibraryView()
        {
            InitializeComponent();
        }


        public bool IsRendered { get; private set; }
        public async Task Render()
        {
            _exercises = new DrawnListCategories(new ListCategoriesExercisesViewModel(), 150);
            _practices = new DrawnListCategories(new ListCategoriesPracticesViewModel(), 500);
            _topics = new DrawnListCategories(new ListCategoriesTopicsViewModel(), 750);
            _breath = new DrawnBreathTab();

            UpdateTabsUponChecks();

            TabsContainer.Children.Add(_exercises);
            TabsContainer.Children.Add(_practices);
            TabsContainer.Children.Add(_topics);
            TabsContainer.Children.Add(_breath);

            await Task.Delay(10);

            IsRendered = true;

            App.UpdateState();
        }


        #region Переключение разделов

        private LibrarySectionType librarySectionType = LibrarySectionType.Exercises;
        public LibrarySectionType LibrarySectionType
        {
            get => librarySectionType;
            set
            {
                if (librarySectionType != value)
                {
                    librarySectionType = value;
                    OnPropertyChanged();
                    if (value == LibrarySectionType.Exercises)
                    {
                        SelectedTab = 0;
                    }
                    else
                    if (value == LibrarySectionType.Practices)
                    {
                        SelectedTab = 1;
                    }
                    else
                    if (value == LibrarySectionType.Topics)
                    {
                        SelectedTab = 2;
                    }
                    else
                    {
                        SelectedTab = 3;
                    }
                }
            }
        }

        private int _SelectedTab;
        public int SelectedTab
        {
            get
            {
                return _SelectedTab;
            }
            set
            {
                if (_SelectedTab != value)
                {
                    _SelectedTab = value;
                    OnPropertyChanged();
                    if (value == 0)
                    {
                        SetExercisesVisible();
                    }
                    else
                    if (value == 1)
                    {
                        SetPracticesVisible();
                    }
                    else
                    if (value == 2)
                    {
                        SetTopicsVisible();
                    }
                    else
                    {
                        SetBreathVisible();
                    }
                }
            }
        }


        public void UpdateTabsUponChecks()
        {
            if (LibrarySectionType == LibrarySectionType.Exercises)
            {
                SetExercisesVisible();
            }
            else
            if (LibrarySectionType == LibrarySectionType.Practices)
            {
                SetPracticesVisible();
            }
            else
            if (LibrarySectionType == LibrarySectionType.Topics)
            {
                SetTopicsVisible();
            }
            else
            {
                SetBreathVisible();
            }
        }

        protected void SetExercisesVisible()
        {
            LibrarySectionType = LibrarySectionType.Exercises;

            if (_exercises == null)
                return;

            _breath.IsVisible = false;
            _exercises.IsVisible = true;
            _practices.IsVisible = false;
            _topics.IsVisible = false;



            App.UpdateState();
        }

        protected void SetPracticesVisible()
        {
            LibrarySectionType = LibrarySectionType.Practices;

            if (_practices == null)
                return;

            _breath.IsVisible = false;
            _exercises.IsVisible = false;
            _practices.IsVisible = true;
            _topics.IsVisible = false;

            App.UpdateState();
        }

        protected void SetTopicsVisible()
        {
            LibrarySectionType = LibrarySectionType.Topics;

            if (_topics == null)
                return;

            _breath.IsVisible = false;
            _exercises.IsVisible = false;
            _practices.IsVisible = false;
            _topics.IsVisible = true;

            App.UpdateState();
        }

        protected void SetBreathVisible()
        {
            LibrarySectionType = LibrarySectionType.BreathPractice;

            if (_breath == null)
                return;

            _breath.IsVisible = true;
            _exercises.IsVisible = false;
            _practices.IsVisible = false;
            _topics.IsVisible = false;

            App.UpdateState();
        }


        #endregion


        private ICommand goToSearch;
        public ICommand GoToSearch
        {
            get => goToSearch ??= new RelayCommand(async obj =>
            {
                if (TouchEffect.CheckLockAndSet())
                    return;

                App.OpenPage(new SearchPage());
            });
        }

        private ICommand goToFavorites;
        public ICommand GoToFavorites
        {
            get => goToFavorites ??= new RelayCommand(async obj =>
            {
                if (TouchEffect.CheckLockAndSet())
                    return;

                var mainPage = App.Current.MainPage.Navigation.NavigationStack.LastOrDefault() as MainPage;
                mainPage.SetView(new FavoritesView());
            });
        }

        public ICommand CommandSelectTab
        {
            get
            {
                return new Command((context) =>
                {
                    var tab = context.ToString().ToInteger();
                    SelectedTab = tab;
                });
            }
        }




    }
}