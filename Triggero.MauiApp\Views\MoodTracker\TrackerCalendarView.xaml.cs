﻿using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions;
using Triggero.MauiMobileApp.Views.Pages.MoodTracker;
using Triggero.Models.MoodTracker.User;
using Syncfusion.Maui.Calendar;

namespace Triggero.MauiMobileApp.Views.MoodTracker
{
    public partial class TrackerCalendarView : ContentView
    {

        public TrackerCalendarView()
        {
            InitializeComponent();

            // TODO: FirstDayOfWeek is now in MonthView, set via XAML or code
            calendar.MonthView.FirstDayOfWeek = DayOfWeek.Monday;
            // TODO: OnMonthCellLoaded is not available in MAUI, use CellTemplate or ViewChanged event
            // calendar.ViewChanged += Calendar_ViewChanged;
            calendar.SelectedDate = null;
        }

        public List<CustomDayView> RecycledCells = new();


        private bool _IsBusy;
        public bool IsBusy
        {
            get
            {
                return _IsBusy;
            }
            set
            {
                if (_IsBusy != value)
                {
                    _IsBusy = value;
                    OnPropertyChanged();

                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        if (value)
                        {
                            calendar.Opacity = 0.5;
                        }
                        else
                        {
                            calendar.Opacity = 1.0;
                        }
                    });

                }
            }
        }


        #region Render

        private List<MoodtrackerItem> monthItems = new List<MoodtrackerItem>();

        public void SetMonthItems(int year, int month)
        {
            Task.Run(async () =>
            {
                try
                {
                    IsBusy = true;
                    await Task.Delay(10);

                    var monthStart = new DateTime(year, month, 1);
                    var monthEnd = new DateTime(year, month, 1).AddMonths(1).AddDays(-1);
                    var loaded = await ApplicationState.Data.GetMoodtrackerItemsAtPeriod(monthStart, monthEnd);

                    MainThread.BeginInvokeOnMainThread(() =>
                    {
                        monthItems = loaded;
                        calendar.DisplayDate = monthStart; // MoveToDate becomes DisplayDate in MAUI
                        // TODO: Refresh() method not available in MAUI, calendar updates automatically
                        monthDateLabel.Text = monthStart.ToString("MMMM yyyy").Capitilize();
                        calendar.SelectedDate = null;

                        IsBusy = false;
                    });

                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    IsBusy = false;
                }
            });

        }

        // TODO: OnMonthCellLoaded event is not available in MAUI Calendar
        // Need to use CellTemplate in XAML or handle ViewChanged event
        // This functionality needs to be reimplemented using MAUI Calendar's CellTemplate
        /*
        private async void Calendar_OnMonthCellLoaded(object sender, Syncfusion.SfCalendar.XForms.MonthCellLoadedEventArgs e)
        {
            var dayItems = monthItems.Where(o => o.Date.Date == e.Date.Date).ToList();

            var cell = new CustomDayView();

            int? mood = null;

            if (dayItems.Any())
            {
                double avg = dayItems.Average(o => o.Mood);
                mood = (int)Math.Round(avg, 0);
            }

            cell.SetContext(e.Date, mood);

            e.View = cell;

        }
        */
        #endregion


        #region Selections

        private void selectionChanged(object sender, CalendarSelectionChangedEventArgs e)
        {
            if (e.NewValue != null && e.NewValue is DateTime selectedDate)
            {
                var found = monthItems.Where(o => o.Date.Date == selectedDate.Date).ToList();
                if (found.Any())
                {
                    App.OpenPage(new TrackerDay(selectedDate.Date, found));
                }
                this.calendar.SelectedDate = null;
            }
        }

        private async void monthChanged(object sender, CalendarViewChangedEventArgs e)
        {
            // In MAUI, ViewChanged event provides NewVisibleDates instead of CurrentValue
            if (e.NewVisibleDates?.Count > 0)
            {
                var firstDate = e.NewVisibleDates[0];
                SetMonthItems(firstDate.Year, firstDate.Month);
            }
        }
        private async void goForwardMonth(object sender, EventArgs e)
        {
            if (IsBusy)
                return;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var date = calendar.DisplayDate.AddMonths(1); // MoveToDate becomes DisplayDate in MAUI
                SetMonthItems(date.Year, date.Month);
            });

        }

        private async void goBackMonth(object sender, EventArgs e)
        {
            if (IsBusy)
                return;

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var date = calendar.DisplayDate.AddMonths(-1); // MoveToDate becomes DisplayDate in MAUI
                SetMonthItems(date.Year, date.Month);
            });


        }

        #endregion


    }
}