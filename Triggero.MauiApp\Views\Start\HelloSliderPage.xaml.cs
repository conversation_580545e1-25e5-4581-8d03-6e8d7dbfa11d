﻿using System.Collections.ObjectModel;
using Syncfusion.Maui.Core.Rotator;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models.Sliders;


namespace Triggero.MauiMobileApp.Views.Pages.Start
{

    public partial class HelloSliderPage : ContentPage
    {
        public HelloSliderPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            Items.Add(new HelloSliderItem
            {
                Text = App.This.Interface.Start.HelloSliderPage.SliderText1,
                ImgPath = "knowYourself.png"
            });
            Items.Add(new HelloSliderItem
            {
                Text = App.This.Interface.Start.HelloSliderPage.SliderText2,
                ImgPath = "practices.png"
            });
            Items.Add(new HelloSliderItem
            {
                Text = App.This.Interface.Start.HelloSliderPage.SliderText3,
                ImgPath = "virtualAssistant.png"
            });


            dotsView.DefaultColor = Color.FromHex("#CEE3F4");
            dotsView.DefaultCornerRadius = 2;
            dotsView.DefaultHeightRequest = 4;
            dotsView.DefaultWidthRequest = 9;

            dotsView.SelectedColor = Color.FromHex("#4D4D4D");
            dotsView.SelectedCornerRadius = 2;
            dotsView.SelectedHeightRequest = 4;
            dotsView.SelectedWidthRequest = 28;

            dotsView.SetDots(Items.Count, 0);
        }

        #region Каруселька
        private ObservableCollection<HelloSliderItem> items = new ObservableCollection<HelloSliderItem>();
        public ObservableCollection<HelloSliderItem> Items
        {
            get { return items; }
            set { items = value; OnPropertyChanged(nameof(Items)); }
        }
        private void onIndexChanged(object sender, SelectedIndexChangedEventArgs e)
        {
            dotsView.SetDots(Items.Count, (int)e.Index);
        }
        #endregion


        private RelayCommand goNext;
        public RelayCommand GoNext
        {
            get => goNext ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new LetsStartPage());
            });
        }


    }
}