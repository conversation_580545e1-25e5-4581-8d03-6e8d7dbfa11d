﻿using Triggero.Domain.Models;
using MobileAPIWrapper;
using CommunityToolkit.Maui.Views;
using CommunityToolkit.Maui;
using System.Linq;
using System.Windows.Input;
using Triggero.Controls;
using Triggero.MauiMobileApp.Views.Pages.Legal;
using Triggero.MauiMobileApp.Views.Popups;
using Triggero.Models.Enums;
using Triggero.Models.Plans;

using Triggero.MauiMobileApp.Extensions.Helpers;

namespace Triggero.MauiMobileApp.Views.Pages.Subscriptions
{

    //BPPage = BuyPlanPage

    public partial class BuyPlanPage : ContentPage
    {
        public BuyPlanPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            Load();
        }

        private async void Load()
        {
            var plans = await ApplicationState.Data.GetPlans();
            var monthPlan = plans.FirstOrDefault(o => o.BuiltInPlanType == BuiltInPlanType.Month);
            var threeMonthsPlan = plans.FirstOrDefault(o => o.BuiltInPlanType == BuiltInPlanType.ThreeMonths);
            var yearPlan = plans.FirstOrDefault(o => o.BuiltInPlanType == BuiltInPlanType.Year);

            monthPlanSlot.Children.Add(new PlanCard(monthPlan, false) { IsSelected = true });
            threeMonthsPlanSlot.Children.Add(new PlanCard(threeMonthsPlan, false));
            yearPlanSlot.Children.Add(new PlanCard(yearPlan, false));
        }

        #region Legal
        private RelayCommand goToEULA;
        public RelayCommand GoToEULA
        {
            get => goToEULA ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new EULAPage());
            });
        }
        private RelayCommand goToTerms;
        public RelayCommand GoToTerms
        {
            get => goToTerms ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new TermsPage());
            });
        }
        private RelayCommand goToPrivacy;
        public RelayCommand GoToPrivacy
        {
            get => goToPrivacy ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new PrivacyPage());
            });
        }
        #endregion

        private ICommand pay;
        public ICommand Pay
        {
            get => pay ??= new RelayCommand(async obj =>
            {
                Plan plan = null;
                if ((monthPlanSlot.Children[0] as PlanCard).IsSelected)
                {
                    plan = (monthPlanSlot.Children[0] as PlanCard).Plan;
                }
                else if ((threeMonthsPlanSlot.Children[0] as PlanCard).IsSelected)
                {
                    plan = (threeMonthsPlanSlot.Children[0] as PlanCard).Plan;
                }
                else if ((yearPlanSlot.Children[0] as PlanCard).IsSelected)
                {
                    plan = (yearPlanSlot.Children[0] as PlanCard).Plan;
                }

                var createdPayment = await TriggeroMobileAPI.Payment.PaySubscription(AuthHelper.UserId, new SubscriptionPaymentSettings
                {
                    Duration = plan.BuiltInPlanType,
                    SubType = SubscriptionType.Full,
                    IsBindingPayment = true,
                });

                var paymentPopup = new YouKassaPopup(createdPayment);
                paymentPopup.PaymentProceed += PaymentPopup_PaymentProceed;
                var popupService = Handler?.MauiContext?.Services?.GetService<IPopupService>();
                if (popupService != null)
                {
                    await popupService.ShowPopupAsync(paymentPopup);
                }
            });
        }
        private async void PaymentPopup_PaymentProceed(object sender, EventArgs e)
        {
            await AuthHelper.ReloadUser();
        }


        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
    }
}