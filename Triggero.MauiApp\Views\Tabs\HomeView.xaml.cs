﻿using AppoMobi.Maui.Gestures;
using AppoMobi.Specials;
using System.Windows.Input;
using DrawnUi.Draw;
using Triggero.Controls.Cards.TasksForToday;
using Triggero.Controls.Other;
using Triggero.MauiPort.Views.Pages;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Views.Drawn;
using Triggero.MauiMobileApp.Views.Pages;
using Triggero.MauiMobileApp.Views.Pages.Library;
using Triggero.MauiMobileApp.Views.Pages.Profile;
using Triggero.MauiMobileApp.Views.Pages.Subscriptions;
using Triggero.MauiMobileApp.Views.Pages.Tests;
using Triggero.Models;
using Triggero.Models.General;
using Triggero.Models.General.Influence;



namespace Triggero.MauiMobileApp.Views
{

    public interface ISubView
    {
        void Render();
    }

    public partial class HomeView : ISubView
    {
        public HomeView()
        {
            try
            {
                InitializeComponent();

                GlobalEvents.UserPropertyChanged += GlobalEvents_UserPropertyChanged;
                GlobalEvents.TodayDataChanged += GlobalEvents_TodayDataChanged;
            }
            catch (Exception e)
            {
                Super.DisplayException(this, e);
            }
        }

        public void Render()
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                RenderUserData(AuthHelper.User);
            });
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                RenderRecommendations();
            });
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                RenderTasksForToday();
            });

        }

        private void GlobalEvents_TodayDataChanged(object sender, EventArgs e)
        {
            MainThread.BeginInvokeOnMainThread(() =>
            {
                try
                {
                    RenderRecommendations();
                    RenderTasksForToday();
                }
                catch (Exception e)
                {
                    Super.Log(e);
                }

            });
        }

        private void GlobalEvents_UserPropertyChanged(object sender, User e)
        {
            RenderUserData(e);
        }

        private void RenderUserData(User user)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                try
                {
                    if (user != null)
                    {
                        hiNameLabel.Text = string.Format(App.This.Interface.MainPage.HelloName, user.Name);
                        BlurIfNotPaid();
                        avatar.Source = App.GetFullImageUrl(user.Avatar.AvatarPath, ThumbnailSize.Small, ThumbnailType.Png);
                        //await ResorcesHelper.GetImageSource(user.Avatar.AvatarPath);

                        RenderNeedToHandleItems();
                    }
                    else
                    {
                        needToHandleFrame.IsVisible = false;
                    }
                }
                catch (Exception e)
                {
                    Super.Log(e);
                }

            });
        }

        #region NeedToHandle items

        private async Task RenderNeedToHandleItems()
        {
            var items = await ApplicationState.Data.GetWhatNeedToHandle();
            MainThread.BeginInvokeOnMainThread(() =>
            {
                try
                {
                    needToHandleItemsLayout.Children.Clear();
                    foreach (var item in items)
                    {
                        var card = new NeedToHandleItem(item)
                        {
                            HeightRequest = 35,
                            VerticalOptions = LayoutOptions.Center,
                            Margin = new Thickness(0, 10, 10, 0)
                        };
                        card.Tapped += Card_Tapped;

                        needToHandleItemsLayout.Children.Add(card);
                    }
                    needToHandleFrame.IsVisible = items.Any();
                }
                catch (Exception e)
                {
                    Super.Log(e);
                    needToHandleFrame.IsVisible = false;
                }

            });
        }

        private void Card_Tapped(object sender, NeedToHandle e)
        {
            App.OpenPage(new SearchPage(e.Title));
        }
        #endregion

        public void BlurIfNotPaid()
        {
            var block = !AuthHelper.IsSubscriptionActive;
            var blockTests = !AuthHelper.IsTestsActivated;
            var blockLibrary = !AuthHelper.IsLibraryActivated;

            MainThread.BeginInvokeOnMainThread(() =>
            {
                if (_effectBlur == null)
                {
                    _effectBlur = new BlurEffect
                    {
                        Amount = 10
                    };
                }

                //tasks
                if (block)
                {
                    TasksFrame.InputTransparent = true;
                    TodoLock.IsVisible = true;
                    if (!TasksFrame.VisualEffects.Contains(_effectBlur))
                    {
                        TasksFrame.VisualEffects.Add(_effectBlur);
                    }
                }
                else
                {
                    TasksFrame.InputTransparent = false;
                    TodoLock.IsVisible = false;
                    if (TasksFrame.VisualEffects.Count > 0)
                        TasksFrame.VisualEffects.Clear();
                }

                //TAGS legacy
                needToHandleGrid.IsVisible = AuthHelper.IsSubscriptionActive;

                //tests
                if (blockTests)
                {
                    btnTests.InputTransparent = true;
                    btnTestLock.IsVisible = true;
                    if (!btnTests.VisualEffects.Contains(_effectBlur))
                    {
                        btnTests.VisualEffects.Add(_effectBlur);
                    }
                }
                else
                {
                    btnTests.InputTransparent = false;
                    btnTestLock.IsVisible = false;
                    if (btnTests.VisualEffects.Count > 0)
                        btnTests.VisualEffects.Clear();
                }

                //breath
                if (blockLibrary)
                {
                    btnBreath.InputTransparent = true;
                    btnBreathLock.IsVisible = true;
                    if (!btnBreath.VisualEffects.Contains(_effectBlur))
                    {
                        btnBreath.VisualEffects.Add(_effectBlur);
                    }
                }
                else
                {
                    btnBreath.InputTransparent = false;
                    btnBreathLock.IsVisible = false;
                    if (btnBreath.VisualEffects.Count > 0)
                        btnBreath.VisualEffects.Clear();
                }

                //practices
                if (blockLibrary)
                {
                    btnPractices.InputTransparent = true;
                    btnLibraryLock.IsVisible = true;
                    if (!btnPractices.VisualEffects.Contains(_effectBlur))
                    {
                        btnPractices.VisualEffects.Add(_effectBlur);
                    }
                }
                else
                {
                    btnPractices.InputTransparent = false;
                    btnLibraryLock.IsVisible = false;
                    if (btnPractices.VisualEffects.Count > 0)
                        btnPractices.VisualEffects.Clear();
                }

                //Recommendations
                if (blockLibrary || blockTests)
                {
                    RecommendationsFrame.InputTransparent = true;
                    RecommendationsLock.IsVisible = true;
                    if (!RecommendationsFrame.VisualEffects.Contains(_effectBlur))
                    {
                        RecommendationsFrame.VisualEffects.Add(_effectBlur);
                    }
                }
                else
                {
                    RecommendationsFrame.InputTransparent = false;
                    RecommendationsLock.IsVisible = false;
                    if (RecommendationsFrame.VisualEffects.Count > 0)
                        RecommendationsFrame.VisualEffects.Clear();
                }

            });



        }


        public void RenderRecommendations()
        {
            recommendationsLayout2.ClearChildren();
            foreach (var item in ApplicationState.TodayData.Recommendations)
            {
                RenderRecommendation(item);
            }
        }
        private void RenderRecommendation(RecommendationModel model)
        {
            RecommendationDrawnBase control = null;
            switch (model.GetRecommendationType())
            {
            case RecomendationType.Test:
            control = new TopicRecommendationDrawn(model, model.Test);
            break;

            case RecomendationType.Exercise:
            control = new TopicRecommendationDrawn(model, model.Exercise);
            break;

            case RecomendationType.Practice:
            control = new TopicRecommendationDrawn(model, model.Practice);
            break;

            case RecomendationType.Topic:
            control = new TopicRecommendationDrawn(model, model.Topic);
            break;
            }

            if (control != null)
            {
                control.Tapped += RecommendationControlTapped; //memory leak!
                recommendationsLayout2.Children.Add(control);
            }
        }

        public void RenderTasksForToday()
        {
            foreach (var item in taskForTodayLayoutDrawn.Children)
            {
                if (item is CellTaskForToday cell)
                {
                    cell.ViewModel.CheckedChanged -= TaskForTodayCheckedChanged;
                }
            }

            taskForTodayLayoutDrawn.ClearChildren();


            foreach (var item in ApplicationState.TodayData.TasksForToday)
            {
                RenderTaskForToday(item);
            }

            SetCompletedTasksLabelText();
        }

        private void RenderTaskForToday(TaskForToday model)
        {
            var control = new CellTaskForToday(model);

            control.ViewModel.CheckedChanged += TaskForTodayCheckedChanged;

            taskForTodayLayoutDrawn.AddSubView(control);
        }

        private void SetCompletedTasksLabelText()
        {

            MainThread.BeginInvokeOnMainThread(() =>
            {
                try
                {
                    var completed = ApplicationState.TodayData.TasksForToday.Count(o => o.IsCompleted);
                    var total = ApplicationState.TodayData.TasksForToday.Count;
                    completedTasksLabelDrawn.Text = $"{completed}/{total}";
                }
                catch (Exception e)
                {
                    Super.Log(e);
                }

            });

        }


        private void TaskForTodayCheckedChanged(object sender, TaskForToday e)
        {
            SetCompletedTasksLabelText();
        }

        private void RecommendationControlTapped(object sender, RecommendationModel e)
        {
            switch (e.GetRecommendationType())
            {
            case RecomendationType.Test:
            App.OpenPage(new TestPage(e.Test));
            break;
            case RecomendationType.Practice:
            App.OpenPage(new PracticePage(e.Practice));
            break;
            case RecomendationType.Exercise:
            App.OpenPage(new ExercisePage(e.Exercise));
            break;
            case RecomendationType.Topic:
            App.OpenPage(new TopicPage(e.Topic));
            break;
            }
        }




        private ICommand goToSearch;
        public ICommand GoToSearch
        {
            get => goToSearch ??= new RelayCommand(async obj =>
            {
                if (TouchEffect.CheckLockAndSet())
                    return;

                var pay = false;
                if (AuthHelper.IsSubscriptionEnded)
                {
                    pay = App.OpenNeedToPayNow();
                }

                if (!pay)
                {
                    App.OpenPage(new SearchPage());
                }
            });
        }
        private ICommand goToFavorites;
        public ICommand GoToFavorites
        {
            get => goToFavorites ??= new RelayCommand(async obj =>
            {
                if (TouchEffect.CheckLockAndSet())
                    return;

                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    try
                    {
                        var pay = false;
                        if (AuthHelper.IsSubscriptionEnded)
                        {
                            pay = App.OpenNeedToPayNow();
                        }

                        if (!pay)
                        {
                            var page = App.Current.MainPage.Navigation.NavigationStack.LastOrDefault() as MainPage;
                            page.Footer.UnselectFooter();
                            page.SetView(new FavoritesView());
                        }

                    }
                    catch (Exception e)
                    {
                        Super.Log(e);
                    }

                });


            });
        }




        private ICommand goToProfile;
        public ICommand GoToProfile
        {
            get => goToProfile ??= new RelayCommand(async obj =>
            {

                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    App.OpenPage(new ProfilePage());
                });

            });
        }

        private ICommand goToTests;
        public ICommand GoToTests
        {
            get => goToTests ??= new RelayCommand(async obj =>
            {

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    try
                    {
                        var page = App.Current.MainPage.Navigation.NavigationStack.LastOrDefault() as MainPage;
                        page.Footer.IsTestsPageSelected = true;
                    }
                    catch (Exception e)
                    {
                        Super.Log(e);
                    }


                });

            });
        }
        private ICommand goToPractices;
        public ICommand GoToPractices
        {
            get => goToPractices ??= new RelayCommand(async obj =>
            {

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    try
                    {
                        var page = App.Current.MainPage.Navigation.NavigationStack.LastOrDefault() as MainPage;
                        page.Footer.IsLibraryPageSelected = true;
                        //subview will be created with delay
                        Tasks.StartDelayed(TimeSpan.FromMilliseconds(350), () =>
                        {
                            MainThread.BeginInvokeOnMainThread(() =>
                            {
                                try
                                {
                                    page.SubViewLibrary.SelectedTab = 1;//.SetPracticesVisible();
                                }
                                catch (Exception e)
                                {
                                    Super.Log(e);
                                }
                            });
                        });
                    }
                    catch (Exception e)
                    {
                        Super.Log(e);
                    }

                });

            });
        }
        private ICommand goToBreath;
        private BlurEffect _effectBlur;

        public ICommand GoToBreath
        {
            get => goToBreath ??= new RelayCommand(async obj =>
            {

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    try
                    {
                        var page = App.Current.MainPage.Navigation.NavigationStack.LastOrDefault() as MainPage;
                        page.Footer.IsLibraryPageSelected = true;
                        //subview will be created with delay
                        Tasks.StartDelayed(TimeSpan.FromMilliseconds(350), () =>
                        {
                            MainThread.BeginInvokeOnMainThread(() =>
                            {
                                try
                                {
                                    page.SubViewLibrary.SelectedTab = 3;
                                }
                                catch (Exception e)
                                {
                                    Super.Log(e);
                                }
                            });
                        });
                    }
                    catch (Exception e)
                    {
                        Super.Log(e);
                    }
                });

            });
        }


        private void ScrollView_Scrolled(object sender, ScrolledEventArgs e)
        {
            var translate = e.ScrollY * 0.33;
            var translatationY = Math.Clamp(translate, 0, 50);
            Header.TranslationY = translatationY;
        }
    }
}