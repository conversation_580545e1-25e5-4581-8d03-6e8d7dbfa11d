
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Windows.Input;


namespace Triggero.MauiMobileApp.Views.Pages.Profile.Notifications
{

    public partial class SetNotificationDatePage : ContentPage
    {
        private TimeSpan _ts;
        public SetNotificationDatePage(TimeSpan ts)
        {
            _ts = ts;

            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            var hoursList = new List<string>();
            var minutesList = new List<string>();

            for (int i = 0; i < 24; i++)
            {
                hoursList.Add(i.ToString());
            }
            for (int i = 0; i < 60; i++)
            {
                minutesList.Add(i.ToString());
            }

            Hours = new ReadOnlyCollection<string>(hoursList);
            Minutes = new ReadOnlyCollection<string>(minutesList);

        }
        protected override void OnAppearing()
        {
            //  hoursPicker.SelectedItemsIndex = new List<int> { _ts.Hours };
            // minutesPicker.SelectedItemsIndex = new List<int> { _ts.Minutes };

            // TODO: In MAUI SfPicker, SelectedIndex is on the PickerColumn, not the SfPicker
            if (hoursSfPicker.Columns.Count > 0)
                hoursSfPicker.Columns[0].SelectedIndex = _ts.Hours;
            if (minutesSfPicker.Columns.Count > 0)
                minutesSfPicker.Columns[0].SelectedIndex = _ts.Minutes;
        }

        private ReadOnlyCollection<string> hours;
        public ReadOnlyCollection<string> Hours
        {
            get { return hours; }
            set { hours = value; OnPropertyChanged(nameof(Hours)); }
        }
        private ReadOnlyCollection<string> minutes;
        public ReadOnlyCollection<string> Minutes
        {
            get { return minutes; }
            set { minutes = value; OnPropertyChanged(nameof(Minutes)); }
        }


        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
        private ICommand selectDate;
        public ICommand SelectDate
        {
            get => selectDate ??= new RelayCommand(async obj =>
            {
                // TODO: In MAUI SfPicker, SelectedIndex is on the PickerColumn, not the SfPicker
                int hours = hoursSfPicker.Columns.Count > 0 ? hoursSfPicker.Columns[0].SelectedIndex : 0;
                int minutes = minutesSfPicker.Columns.Count > 0 ? minutesSfPicker.Columns[0].SelectedIndex : 0;


                Selected?.Invoke(this, new TimeSpan(hours, minutes, 0));
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }


        public event EventHandler<TimeSpan> Selected;

    }
}