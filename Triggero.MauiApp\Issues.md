# Migration Issues and TODOs

This document tracks all the compromises, missing functionality, and TODO items that were left behind during the Xamarin to MAUI migration process.

## Syncfusion Controls Migration Issues

### 1. SfCalendar (TrackerCalendarView.xaml/.cs)

#### Missing Cell Customization
- **Issue**: `OnMonthCellLoaded` event is not available in MAUI SfCalendar
- **Impact**: Custom cell rendering for mood tracker calendar is currently disabled
- **Location**: `TrackerCalendarView.xaml.cs` lines 97-120
- **Workaround Needed**: Implement cell customization using `CellTemplate` property
- **Code Affected**:
  ```csharp
  // TODO: OnMonthCellLoaded event is not available in MAUI Calendar
  // Need to use CellTemplate in XAML or handle ViewChanged event
  // This functionality needs to be reimplemented using MAUI Calendar's CellTemplate
  /*
  private async void Calendar_OnMonthCellLoaded(object sender, Syncfusion.SfCalendar.XForms.MonthCellLoadedEventArgs e)
  {
      var dayItems = monthItems.Where(o => o.Date.Date == e.Date.Date).ToList();
      var cell = new CustomDayView();
      int? mood = null;
      if (dayItems.Any())
      {
          double avg = dayItems.Average(o => o.Mood);
          mood = (int)Math.Round(avg, 0);
      }
      cell.SetContext(e.Date, mood);
      e.View = cell;
  }
  */
  ```

#### Missing Refresh Method
- **Issue**: `calendar.Refresh()` method is not available in MAUI
- **Impact**: Manual calendar refresh is no longer possible
- **Location**: `TrackerCalendarView.xaml.cs` line 81
- **Status**: Calendar updates automatically in MAUI, but may need verification

#### FirstDayOfWeek Property Location Change
- **Issue**: Property moved from SfCalendar to MonthView
- **Status**: ✅ Fixed - Updated to use `calendar.MonthView.FirstDayOfWeek`
- **Location**: `TrackerCalendarView.xaml.cs` line 19

### 2. SfPicker (SetNotificationDatePage.xaml/.cs)

#### Unsupported Styling Properties
- **Issue**: Multiple styling properties are not supported in MAUI SfPicker
- **Impact**: Visual appearance may differ from Xamarin version
- **Location**: `SetNotificationDatePage.xaml` lines 95-96, 117-118
- **Missing Properties**:
  - `ColumnHeaderBackgroundColor`
  - `HeaderBackgroundColor` 
  - `BorderColor`
  - `ShowHeader`
  - `UnSelectedItemFontSize`
  - `SelectedItemTextColor`
  - `UnSelectedItemTextColor`
  - `SelectedItemFontSize`

#### API Structure Changes
- **Issue**: `SelectedIndex` property moved from SfPicker to PickerColumn
- **Status**: ✅ Fixed - Updated to use `picker.Columns[0].SelectedIndex`
- **Location**: `SetNotificationDatePage.xaml.cs` lines 41-45, 75-77

#### Required Columns Structure
- **Issue**: MAUI SfPicker requires explicit Columns collection
- **Status**: ✅ Fixed - Added `<syncfusion:SfPicker.Columns>` wrapper
- **Location**: `SetNotificationDatePage.xaml` lines 97-99, 119-121

### 3. SfRotator (HelloSliderPage.xaml/.cs)

#### Property Name Changes
- **Issue**: `DotsBorderColor` renamed to `DotsStroke` in MAUI
- **Status**: ⚠️ Not used in current implementation, but may be needed later
- **Impact**: If dots styling is needed, property name must be updated

## General Migration Patterns Established

### Namespace Updates
- ✅ `Syncfusion.SfCalendar.XForms` → `Syncfusion.Maui.Calendar`
- ✅ `Syncfusion.SfRotator.XForms` → `Syncfusion.Maui.Rotator`  
- ✅ `Syncfusion.SfPicker.XForms` → `Syncfusion.Maui.Picker`

### Event Handler Updates
- ✅ `MonthChanged` → `ViewChanged` (Calendar)
- ✅ Event argument types updated for MAUI compatibility

### Property Migrations
- ✅ `MoveToDate` → `DisplayDate` (Calendar)
- ✅ `PickerMode` → `Mode` (Picker)
- ✅ `MonthViewSettings` → `CalendarMonthView` (Calendar)

## Priority Actions Needed

### High Priority
1. **Implement Calendar Cell Customization**: Research and implement `CellTemplate` for mood tracker calendar
2. **Verify Calendar Auto-Refresh**: Test that calendar updates properly without manual refresh calls
3. **Style SfPicker Controls**: Implement proper styling using MAUI TextStyle and SelectedTextStyle properties

### Medium Priority
1. **Test Visual Consistency**: Compare visual appearance with original Xamarin version
2. **Performance Testing**: Verify that MAUI controls perform adequately
3. **Update Documentation**: Document the new MAUI-specific patterns for future development

### Low Priority
1. **Explore Advanced Features**: Investigate if any new MAUI-specific features could improve functionality
2. **Code Cleanup**: Remove commented-out code once replacement functionality is confirmed working

## Popup Migration Issues

### CommunityToolkit.Maui Popup v2 Migration
- **Status**: ✅ Completed successfully
- **Files Migrated**: `YouKassaPopup.xaml/.cs`, `ConfirmationPopup.xaml/.cs`, `AlertPopup.xaml/.cs`
- **Navigation Updates**: All popup navigation calls updated across multiple files
- **Breaking Changes**: Migrated from `Rg.Plugins.Popup.Pages.PopupPage` to `CommunityToolkit.Maui.Views.Popup`

## WebView Migration Issues

### Microsoft.AspNetCore.Components.WebView.Maui
- **Status**: ✅ Completed successfully
- **Files Updated**: Multiple WebView implementations migrated from Xamarin.Forms.WebView
- **Namespace Changes**: Updated to use MAUI WebView controls

## Platform-Specific Issues

### Missing Platform Services
- **Issue**: Various Xamarin platform-specific services need MAUI equivalents
- **Impact**: Platform-specific functionality may be broken
- **Examples**:
  - File system access patterns
  - Device-specific APIs
  - Platform renderers converted to handlers
- **Status**: 🔴 Needs comprehensive audit

### Dependency Injection Changes
- **Issue**: Xamarin.Forms DI vs MAUI DI patterns
- **Impact**: Service registration and resolution may need updates
- **Location**: `MauiProgram.cs` and throughout app
- **Status**: 🟡 Needs review

## UI/UX Migration Issues

### Custom Renderers to Handlers
- **Issue**: Xamarin custom renderers need conversion to MAUI handlers
- **Impact**: Custom UI behavior may be lost
- **Status**: 🔴 Needs identification and conversion of all custom renderers

### Layout Differences
- **Issue**: MAUI layout behavior may differ from Xamarin.Forms
- **Impact**: UI may not render exactly the same
- **Status**: 🟡 Needs visual testing across all screens

### Font and Resource Handling
- **Issue**: Font registration and resource loading differences
- **Impact**: Fonts and images may not load correctly
- **Status**: 🟡 Needs verification

## Navigation Issues

### Shell Navigation Changes
- **Issue**: MAUI Shell navigation patterns may differ
- **Impact**: Navigation flow may be broken
- **Status**: 🟡 Needs testing

### Modal and Popup Navigation
- **Issue**: Modal presentation differences between platforms
- **Impact**: Popup behavior may be inconsistent
- **Status**: ⚠️ Partially addressed with CommunityToolkit.Maui

## Performance Issues

### Startup Performance
- **Issue**: MAUI startup time may differ from Xamarin
- **Impact**: App launch experience
- **Status**: 🟡 Needs measurement and optimization

### Memory Usage
- **Issue**: MAUI memory patterns may differ
- **Impact**: App performance and stability
- **Status**: 🟡 Needs profiling

## Third-Party Library Issues

### NuGet Package Compatibility
- **Issue**: Some Xamarin packages may not have MAUI equivalents
- **Impact**: Features may be missing or need alternative implementations
- **Status**: 🟡 Ongoing - needs package-by-package review

### Plugin Compatibility
- **Issue**: Xamarin plugins need MAUI equivalents
- **Examples**: Camera, file picker, permissions, etc.
- **Status**: 🔴 Needs comprehensive plugin audit

## Build and Deployment Issues

### Platform Target Differences
- **Issue**: MAUI platform targeting vs Xamarin
- **Impact**: Build configuration and deployment
- **Status**: 🟡 Windows-first approach adopted, other platforms TBD

### Code Signing and Certificates
- **Issue**: Platform-specific signing requirements
- **Impact**: App store deployment
- **Status**: 🟡 Not yet addressed

## Testing Issues

### Unit Test Migration
- **Issue**: Test frameworks and mocking may need updates
- **Impact**: Test coverage and CI/CD
- **Status**: 🔴 Not yet addressed

### UI Testing
- **Issue**: UI test frameworks for MAUI
- **Impact**: Automated testing capability
- **Status**: 🔴 Not yet addressed

## Documentation and Knowledge Transfer

### Migration Documentation
- **Issue**: Need comprehensive migration guide
- **Impact**: Future development and maintenance
- **Status**: 🟡 Partially documented in various .md files

### Developer Onboarding
- **Issue**: New developers need MAUI-specific guidance
- **Impact**: Development velocity
- **Status**: 🔴 Needs creation

## Current Compilation Errors (Priority Order)

### 🔴 XAML Namespace Issues (Immediate Fix Needed)
1. **'abstractions' undeclared prefix** - Multiple XAML files
   - Impact: XAML compilation completely blocked
   - Likely missing DrawnUI namespace declarations

2. **'views' undeclared prefix** - Lines 183, 255, 276
   - Impact: XAML compilation blocked
   - Likely missing DrawnUI.Maui.Views namespace

### 🔴 Missing Namespace/Assembly References
3. **Syncfusion XForms references** - Still using old Xamarin namespaces
   - `Syncfusion.XForms` namespace not found
   - Need to complete Syncfusion migration

4. **DrawnUI namespace issues**
   - `DrawnUi.Maui` namespace not found
   - `DrawnUi.Maui.Views` namespace missing
   - May need DrawnUI NuGet package installation

5. **Plugin compatibility**
   - `Plugin.Connectivity` - needs MAUI equivalent
   - `Plugin.FirebasePushNotification` - needs MAUI equivalent

### 🔴 Missing Project References/Models
6. **Internal namespace issues**
   - `Triggero.MauiPort.Enums` not found
   - `Triggero.MauiPort.Extensions` not found
   - `Triggero.MauiPort.ViewModels` not found
   - `Triggero.MauiMobileApp.Models` not found

7. **External dependencies**
   - `HtmlAgilityPack` - needs NuGet package
   - `PanCardView` - needs MAUI equivalent
   - `BaseQuestionView`, `BaseRecommendationView` - missing base classes

### 🟡 API Compatibility Issues
8. **Method signature changes**
   - `TestsCategoriesDrawn.Draw()` - DrawnUI API change
   - `SelectedIndexChangedEventArgs` - Syncfusion API change

9. **Platform-specific references**
   - `Android` namespace missing (defer to later)
   - `Preserve` attribute missing

## Immediate Action Plan

### Phase 1: XAML Namespace Fixes (Start Here)
1. Fix 'abstractions' and 'views' undeclared prefixes
2. Add proper DrawnUI namespace declarations
3. Complete remaining Syncfusion namespace updates

### Phase 2: Missing Dependencies
1. Install missing NuGet packages (HtmlAgilityPack, etc.)
2. Verify DrawnUI package installation
3. Find MAUI equivalents for Plugin.* packages

### Phase 3: Internal Project Structure
1. Fix internal namespace references
2. Ensure all Models, ViewModels, Extensions are properly referenced
3. Update project structure if needed

### Phase 4: API Compatibility
1. Fix DrawnUI API changes
2. Complete Syncfusion API updates
3. Handle breaking changes

## Priority Matrix

### 🔴 Critical (Blocks Compilation)
1. XAML namespace issues
2. Missing DrawnUI references
3. Incomplete Syncfusion migration
4. Missing internal namespaces

### 🟡 Important (Affects User Experience)
1. SfPicker styling
2. UI layout verification
3. Navigation testing
4. Performance profiling

### ⚠️ Monitor (Needs Verification)
1. Calendar auto-refresh behavior
2. Font and resource loading
3. Memory usage patterns

### ✅ Completed
1. Popup migration to CommunityToolkit.Maui v2
2. WebView migration
3. Basic Syncfusion namespace updates

## Notes for Future Development

- Always check Syncfusion MAUI documentation before implementing new features
- MAUI Syncfusion controls have different property structures - avoid direct property mapping assumptions
- Cell customization in MAUI requires template-based approach rather than event-based approach
- Keep this document updated as issues are resolved or new ones are discovered
- Use Windows-first development workflow for faster iteration
- Document all compromises and TODOs in this file for future reference
