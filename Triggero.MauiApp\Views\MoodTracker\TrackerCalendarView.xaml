﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="Triggero.MauiMobileApp.Views.MoodTracker.TrackerCalendarView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:syncfusion="clr-namespace:Syncfusion.Maui.Calendar;assembly=Syncfusion.Maui.Calendar">
    <ContentView.Content>
        <Grid>



            <syncfusion:SfCalendar
                x:Name="calendar"
                Margin="{OnPlatform Android='0',
                                    iOS='0,15,0,0'}"
                BackgroundColor="White"
                ViewChanged="monthChanged"
                SelectionChanged="selectionChanged"
                SelectionMode="Single"
                ShowTrailingAndLeadingDates="False"
                AllowViewNavigation="False"
                View="Month">

                <syncfusion:SfCalendar.MonthView>
                    <syncfusion:CalendarMonthView
                        Background="Black"
                        TodayBackground="Transparent"
                        TrailingLeadingDatesBackground="Transparent"
                        DisabledDatesBackground="Transparent"
                        WeekendDatesBackground="Transparent">

                        <!-- TODO: Map remaining properties from Xamarin to MAUI equivalents -->
                        <!-- AgendaSelectedDateColor, InlineBackgroundColor not supported in MAUI -->
                        <!-- DateSelectionColor, SelectionRadius handled by SfCalendar.SelectionBackground -->

                        <syncfusion:CalendarMonthView.TextStyle>
                            <syncfusion:CalendarTextStyle TextColor="Black" />
                        </syncfusion:CalendarMonthView.TextStyle>

                        <syncfusion:CalendarMonthView.SelectionTextStyle>
                            <syncfusion:CalendarTextStyle TextColor="Transparent" />
                        </syncfusion:CalendarMonthView.SelectionTextStyle>

                        <syncfusion:CalendarMonthView.HeaderView>
                            <syncfusion:CalendarMonthHeaderView Background="Transparent">
                                <syncfusion:CalendarMonthHeaderView.TextStyle>
                                    <syncfusion:CalendarTextStyle TextColor="White" />
                                </syncfusion:CalendarMonthHeaderView.TextStyle>
                            </syncfusion:CalendarMonthHeaderView>
                        </syncfusion:CalendarMonthView.HeaderView>

                    </syncfusion:CalendarMonthView>
                </syncfusion:SfCalendar.MonthView>
            </syncfusion:SfCalendar>

            <Grid
                BackgroundColor="White"
                HeightRequest="45"
                HorizontalOptions="Fill"
                VerticalOptions="Start">

                <Label
                    x:Name="monthDateLabel"
                    Margin="12,20,0,0"
                    FontAttributes="Bold"
                    FontSize="{OnPlatform Android=17,
                                          iOS=17}"
                    Text="dfsdsf"
                    TextColor="#0F2552" />

                <Grid
                    Margin="0,20,27,0"
                    HeightRequest="25"
                    HorizontalOptions="End"
                    VerticalOptions="Start"
                    WidthRequest="90">

                    <ImageButton
                        Padding="0,6"
                        BackgroundColor="Transparent"
                        Clicked="goBackMonth"
                        CornerRadius="0"
                        HeightRequest="30"
                        HorizontalOptions="Start"
                        Source="arrowBackBlack.png"
                        VerticalOptions="Center"
                        WidthRequest="30" />

                    <ImageButton
                        Padding="0,6"
                        BackgroundColor="Transparent"
                        Clicked="goForwardMonth"
                        CornerRadius="0"
                        HeightRequest="30"
                        HorizontalOptions="End"
                        Source="arrowForwardBlack.png"
                        VerticalOptions="Center"
                        WidthRequest="30" />

                </Grid>

            </Grid>

        </Grid>
    </ContentView.Content>
</ContentView>