﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:sliders="clr-namespace:Triggero.Controls.Cards.Sliders" 
             xmlns:custom="clr-namespace:Triggero.Custom" 
             xmlns:syncfusion="clr-namespace:Syncfusion.Maui.Rotator;assembly=Syncfusion.Maui.Rotator"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Class="Triggero.MauiMobileApp.Views.Pages.Start.HelloSliderPage"
             x:Name="this">
    <ContentPage.Content>
        <Grid Padding="0,30,0,0">

            <Image
                Margin="0,-30,0,0"
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="lightBlueGradientBg.png"/>

            <Label 
                Margin="0,50,20,0"
                HorizontalOptions="End"
                VerticalOptions="Start"
                TextColor="{x:StaticResource greyTextColor}"
                Opacity="0.5"
                FontSize="{OnPlatform Android=14,iOS=14}"
                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Start.HelloSliderPage.Skip}"
                TextDecorations="Underline"
                TextTransform="None">
                <Label.GestureRecognizers>
                    <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoNext}"/>
                </Label.GestureRecognizers>
            </Label>

            <StackLayout
                Spacing="0"
                VerticalOptions="Center">



                <Grid  
                    Margin="0,35,0,0"
                    HeightRequest="400"
                    VerticalOptions="Start"
                    HorizontalOptions="Fill">

                    <syncfusion:SfRotator
                        DotPlacement="None"
                        WidthRequest="330"
                        SelectedIndexChanged="onIndexChanged"
                        ItemsSource="{Binding Source={x:Reference this},Path=Items}"
                        HorizontalOptions="Center">
                        <syncfusion:SfRotator.ItemTemplate>
                            <DataTemplate>
                                <sliders:HelloSliderItemCard HelloSliderItem="{Binding}"/>
                            </DataTemplate>
                        </syncfusion:SfRotator.ItemTemplate>
                    </syncfusion:SfRotator>
                </Grid>

                <custom:DotsView 
                    Margin="0,20,0,0"
                    HeightRequest="10"
                    VerticalOptions="Start"
                    HorizontalOptions="Center"
                    x:Name="dotsView"/>



                <ImageButton 
                    Command="{Binding Source={x:Reference this},Path=GoNext}"
                    Margin="0,122,0,0"
                    WidthRequest="92"
                    HeightRequest="64"
                    HorizontalOptions="Center"
                    VerticalOptions="Start"
                    CornerRadius="0"
                    BackgroundColor="Transparent"
                    Source="yellowContinueBtn.png"/>


            </StackLayout>
            
            
            
            

        </Grid>
    </ContentPage.Content>
</ContentPage>