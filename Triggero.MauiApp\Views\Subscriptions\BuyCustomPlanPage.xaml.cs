﻿using MobileAPIWrapper;
using CommunityToolkit.Maui.Views;
using CommunityToolkit.Maui;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Input;
using Triggero.Controls;
using Triggero.Domain.Models;
using Triggero.MauiMobileApp.Views.Pages.Legal;
using Triggero.MauiMobileApp.Views.Popups;
using Triggero.Models.Plans;

namespace Triggero.MauiMobileApp.Views.Pages.Subscriptions
{

    //BCPPage = BuyCustomPlanPage

    public partial class BuyCustomPlanPage : ContentPage
    {
        private CustomPlanSelectionsModel _selectedOptions;
        public BuyCustomPlanPage(CustomPlanSelectionsModel selectedOptions)
        {
            _selectedOptions = selectedOptions;
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);

            RenderPlanOptions(selectedOptions);
        }

        private async void RenderPlanOptions(CustomPlanSelectionsModel selectedOptions)
        {
            double sum = 0;

            var options = await ApplicationState.Data.GetPlansOptions();

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                try
                {
                    planOptionsLayout.Children.Clear();

                    foreach (var option in options)
                    {

                        var card = new PlanOptionCard(option, true)
                        {
                            HeightRequest = 83,
                            VerticalOptions = LayoutOptions.Start
                        };
                        card.SelectionChanged += Card_SelectionChanged;

                        switch (option.Type)
                        {
                            case PlanOptionType.Library:
                            card.IsSelected = selectedOptions.IsLibrarySelected;
                            break;
                            case PlanOptionType.ChatBot:
                            card.IsSelected = selectedOptions.IsChatBotSelected;
                            break;
                            case PlanOptionType.MoodTracker:
                            card.IsSelected = selectedOptions.IsMoodTrackerSelected;
                            break;
                            case PlanOptionType.Tests:
                            card.IsSelected = selectedOptions.IsTestsSelected;
                            break;
                        }

                        if (card.IsSelected)
                        {
                            sum += option.Price;
                        }

                        planOptionsLayout.Children.Add(card);
                    }


                    var plans = await ApplicationState.Data.GetPlans();
                    var monthPlan = plans.FirstOrDefault(o => o.BuiltInPlanType == BuiltInPlanType.Month);
                    var threeMonthsPlan = plans.FirstOrDefault(o => o.BuiltInPlanType == BuiltInPlanType.ThreeMonths);
                    var yearPlan = plans.FirstOrDefault(o => o.BuiltInPlanType == BuiltInPlanType.Year);


                    monthPlan.Price = Math.Round(sum, 0);
                    threeMonthsPlan.Price = Math.Round(sum * 3, 0);
                    yearPlan.Price = Math.Round(sum * 12, 0);
                    //yearPlan.Discount = 10;

                    monthPlanSlot.Children.Add(new PlanCard(monthPlan, false) { IsSelected = true });
                    threeMonthsPlanSlot.Children.Add(new PlanCard(threeMonthsPlan, false));
                    yearPlanSlot.Children.Add(new PlanCard(yearPlan, false));
                }
                catch (Exception e)
                {
                    Super.Log(e);
                }

            });


        }
        private void Card_SelectionChanged(object sender, bool e)
        {
            var selectionsModel = new CustomPlanSelectionsModel();
            double sum = 0;

            foreach (PlanOptionCard card in planOptionsLayout.Children)
            {
                if (card.IsSelected)
                {
                    sum += card.Option.Price;
                }

                switch (card.Option.Type)
                {
                    case PlanOptionType.Library:
                    selectionsModel.IsLibrarySelected = card.IsSelected;
                    break;
                    case PlanOptionType.ChatBot:
                    selectionsModel.IsChatBotSelected = card.IsSelected;
                    break;
                    case PlanOptionType.MoodTracker:
                    selectionsModel.IsMoodTrackerSelected = card.IsSelected;
                    break;
                    case PlanOptionType.Tests:
                    selectionsModel.IsTestsSelected = card.IsSelected;
                    break;
                }
            }

            if (monthPlanSlot.Children.Any())
            {
                (monthPlanSlot.Children[0] as PlanCard).Plan.Price = Math.Round(sum, 0);
                (threeMonthsPlanSlot.Children[0] as PlanCard).Plan.Price = Math.Round(sum * 3d, 0);
                (yearPlanSlot.Children[0] as PlanCard).Plan.Price = Math.Round(sum * 12d, 0);

                (monthPlanSlot.Children[0] as PlanCard).Render();
                (threeMonthsPlanSlot.Children[0] as PlanCard).Render();
                (yearPlanSlot.Children[0] as PlanCard).Render();
            }


            _selectedOptions = selectionsModel;
        }



        #region Legal
        private RelayCommand goToEULA;
        public RelayCommand GoToEULA
        {
            get => goToEULA ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new EULAPage());
            });
        }
        private RelayCommand goToTerms;
        public RelayCommand GoToTerms
        {
            get => goToTerms ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new TermsPage());
            });
        }
        private RelayCommand goToPrivacy;
        public RelayCommand GoToPrivacy
        {
            get => goToPrivacy ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new PrivacyPage());
            });
        }
        #endregion


        private ICommand pay;
        public ICommand Pay
        {
            get => pay ??= new RelayCommand(async obj =>
            {
                Plan plan = null;
                if ((monthPlanSlot.Children[0] as PlanCard).IsSelected)
                {
                    plan = (monthPlanSlot.Children[0] as PlanCard).Plan;
                }
                else if ((threeMonthsPlanSlot.Children[0] as PlanCard).IsSelected)
                {
                    plan = (threeMonthsPlanSlot.Children[0] as PlanCard).Plan;
                }
                else if ((yearPlanSlot.Children[0] as PlanCard).IsSelected)
                {
                    plan = (yearPlanSlot.Children[0] as PlanCard).Plan;
                }

                var planOptions = await ApplicationState.Data.GetPlansOptions();
                var planOptionIds = new List<int>();
                if (_selectedOptions.IsLibrarySelected)
                {
                    planOptionIds.Add(planOptions.FirstOrDefault(o => o.Type == PlanOptionType.Library).Id);
                }
                if (_selectedOptions.IsMoodTrackerSelected)
                {
                    planOptionIds.Add(planOptions.FirstOrDefault(o => o.Type == PlanOptionType.MoodTracker).Id);
                }
                if (_selectedOptions.IsTestsSelected)
                {
                    planOptionIds.Add(planOptions.FirstOrDefault(o => o.Type == PlanOptionType.Tests).Id);
                }
                if (_selectedOptions.IsChatBotSelected)
                {
                    planOptionIds.Add(planOptions.FirstOrDefault(o => o.Type == PlanOptionType.ChatBot).Id);
                }


                if (planOptionIds.Any())
                {
                    var createdPayment = await TriggeroMobileAPI.Payment.PaySubscription(AuthHelper.UserId, new SubscriptionPaymentSettings
                    {
                        Duration = plan.BuiltInPlanType,
                        SubType = SubscriptionType.Custom,
                        planOptionIds = planOptionIds,
                        IsBindingPayment = true,
                    });

                    var paymentPopup = new YouKassaPopup(createdPayment);
                    paymentPopup.PaymentProceed += PaymentPopup_PaymentProceed;
                    var popupService = Handler?.MauiContext?.Services?.GetService<IPopupService>();
                    if (popupService != null)
                    {
                        await popupService.ShowPopupAsync(paymentPopup);
                    }
                }

            });
        }
        private async void PaymentPopup_PaymentProceed(object sender, EventArgs e)
        {
            await AuthHelper.ReloadUser();
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
    }
}