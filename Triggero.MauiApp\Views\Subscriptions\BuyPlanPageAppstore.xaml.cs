﻿using CRMMobileApp.Core;
using MobileAPI.Models;
using MobileAPI.Models.Enums;
using MobileAPIWrapper;
using CommunityToolkit.Maui.Views;
using CommunityToolkit.Maui;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Input;
using Triggero.Controls;
using Triggero.Helpers;
using Triggero.Models.Enums;
using Triggero.Models.Plans;
using Triggero.Views.Pages.Legal;
using Triggero.Views.Popups;
using TriggeroV2.Helpers;



namespace Triggero.Views.Pages.Subscriptions
{
	[XamlCompilation(XamlCompilationOptions.Compile)]
	public partial class BuyPlanPageAppstore : ContentPage
	{
		public BuyPlanPageAppstore()
		{
			InitializeComponent();
			NavigationPage.SetHasNavigationBar(this, false);

            Load();
        }

		private async void Load()
		{
            var plans = await ApplicationState.Data.GetPlans();
            var monthPlan = plans.FirstOrDefault(o => o.BuiltInPlanType == BuiltInPlanType.Month);
            var yearPlan = plans.FirstOrDefault(o => o.BuiltInPlanType == BuiltInPlanType.Year);

            monthPlan.Discount = 0;
            monthPlan.Price = 499;

            yearPlan.Discount = 0;
            yearPlan.Price = 2990;

            monthPlanSlot.Children.Add(new PlanCard(monthPlan, true) { IsSelected = true });
            yearPlanSlot.Children.Add(new PlanCard(yearPlan, true));

            twoPaymentsGrid.IsVisible = !(await TriggeroMobileAPI.Common.CheckHui());
            onePaymentGrid.IsVisible = !twoPaymentsGrid.IsVisible;
        }

        #region Legal
        private RelayCommand goToEULA;
        public RelayCommand GoToEULA
        {
            get => goToEULA ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushAsync(new EULAPage());
            });
        }
        private RelayCommand goToTerms;
        public RelayCommand GoToTerms
        {
            get => goToTerms ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushAsync(new TermsPage());
            });
        }
        private RelayCommand goToPrivacy;
        public RelayCommand GoToPrivacy
        {
            get => goToPrivacy ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PushAsync(new PrivacyPage());
            });
        }
        #endregion

        private ICommand pay;
        public ICommand Pay
        {
            get => pay ??= new RelayCommand(async obj =>
            {
                Plan plan = null;
                if ((monthPlanSlot.Children[0] as PlanCard).IsSelected)
                {
                    plan = (monthPlanSlot.Children[0] as PlanCard).Plan;
                }
                else if ((yearPlanSlot.Children[0] as PlanCard).IsSelected)
                {
                    plan = (yearPlanSlot.Children[0] as PlanCard).Plan;
                }

                var createdPayment = await TriggeroMobileAPI.Payment.PaySubscription(AuthHelper.UserId, new SubscriptionPaymentSettings
                {
                    Duration = plan.BuiltInPlanType,
                    SubType = SubscriptionType.Full,
                    IsBindingPayment = true,
                    PaymentMethod = PaymentMethodEnum.Appstore
                });

                await PayAppstore(plan, createdPayment);
            });
        }
        private ICommand pay2;
        public ICommand Pay2
        {
            get => pay2 ??= new RelayCommand(async obj =>
            {
                Plan plan = null;
                if ((monthPlanSlot.Children[0] as PlanCard).IsSelected)
                {
                    plan = (monthPlanSlot.Children[0] as PlanCard).Plan;
                }
                else if ((yearPlanSlot.Children[0] as PlanCard).IsSelected)
                {
                    plan = (yearPlanSlot.Children[0] as PlanCard).Plan;
                }

                var createdPayment = await TriggeroMobileAPI.Payment.PaySubscription(AuthHelper.UserId, new SubscriptionPaymentSettings
                {
                    Duration = plan.BuiltInPlanType,
                    SubType = SubscriptionType.Full,
                    IsBindingPayment = true,
                });

                var paymentPopup = new YouKassaPopup(createdPayment);
                paymentPopup.PaymentProceed += PaymentPopup_PaymentProceed;
                var popupService = Handler?.MauiContext?.Services?.GetService<IPopupService>();
                if (popupService != null)
                {
                    await popupService.ShowPopupAsync(paymentPopup);
                }


            });
        }


        private async void PaymentPopup_PaymentProceed(object sender, EventArgs e)
        {
            var user = await TriggeroMobileAPI.GeneralMethods.UserMethods.UsersMethods.GetUserById(AuthHelper.UserId);
            AuthHelper.UserId = user.Id;
            AuthHelper.User = user;

            ApplicationState.ConfigData.UserId = user.Id;
            ApplicationState.ConfigData.User = user;

            GlobalEvents.OnUserPropertyChanged(user);
        }


        private async Task PayAppstore(Plan plan, CreatedPaymentModel createdPayment)
        {
            bool isPaid = false;
            if (plan.BuiltInPlanType == BuiltInPlanType.Year)
            {
                isPaid = await InAppPurchaseHelper.ActivateYearSubscription();
            }
            else if (plan.BuiltInPlanType == BuiltInPlanType.Month)
            {
                isPaid = await InAppPurchaseHelper.ActivateMonthSubscription();
            }

            if (isPaid)
            {
                await TriggeroMobileAPI.Payment.SetPaidViaAppstore(createdPayment.PaymentId);

                var user = await TriggeroMobileAPI.GeneralMethods.UserMethods.UsersMethods.GetUserById(AuthHelper.UserId);
                AuthHelper.UserId = user.Id;
                AuthHelper.User = user;

                ApplicationState.ConfigData.UserId = user.Id;
                ApplicationState.ConfigData.User = user;
                ApplicationState.ConfigData.SaveChangesToMemory();

                GlobalEvents.OnUserPropertyChanged(user);
            }
            else
            {
                await DisplayAlert("Ошибка", "Не удалось оплатить подписку", "Ок");
            }
        }



        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
    }
}