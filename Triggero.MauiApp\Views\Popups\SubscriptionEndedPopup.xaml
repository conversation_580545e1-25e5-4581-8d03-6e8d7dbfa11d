﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:Popup
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    x:Name="this"
    x:Class="Triggero.MauiMobileApp.Views.Popups.SubscriptionEndedPopup">
    <toolkit:Popup.Content>
        <Grid
            Margin="0,187,0,0"
            VerticalOptions="Fill">


            <Image
                Aspect="Fill"
                HorizontalOptions="Fill"
                VerticalOptions="Fill"
                Source="lockedBlur.png"/>

            <ImageButton 
                BackgroundColor="Transparent"
                Command="{Binding Source={x:Reference this},Path=Close}"
                CornerRadius="0"
                Margin="0,25,25,0"
                HorizontalOptions="End"
                VerticalOptions="Start"
                WidthRequest="56"
                HeightRequest="56"
                Source="closeBtnBordered.png"/>

            <StackLayout
                Spacing="0"
                HorizontalOptions="Fill"
                VerticalOptions="Center">
                <Image
                    Aspect="Fill"
                    WidthRequest="43"
                    HeightRequest="60"
                    HorizontalOptions="Center"
                    VerticalOptions="Start"
                    Source="subscriptionLock.png"/>


                <StackLayout
                    Spacing="1"
                    HorizontalOptions="Center"
                    Margin="0,40,0,0">

                    <Label 
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="{OnPlatform Android=14,iOS=17}"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="{OnPlatform Android=216,iOS=260}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Subscriptions.SubscriptionEnded.Text}"/>


                </StackLayout>

                <Button 
                    Command="{Binding Source={x:Reference this},Path=GoToSubscriptions}"
                    VerticalOptions="Start"
                    HeightRequest="56"
                    Margin="20,76,20,0"
                    Style="{x:StaticResource yellow_btn}"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Subscriptions.SubscriptionEnded.Restore}"/>

            </StackLayout>

        </Grid>
    </toolkit:Popup.Content>
</toolkit:Popup>