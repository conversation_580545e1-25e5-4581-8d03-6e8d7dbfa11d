﻿<?xml version="1.0" encoding="utf-8" ?>
<toolkit:Popup
    xmlns:toolkit="http://schemas.microsoft.com/dotnet/2022/maui/toolkit"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    x:Name="this"
    x:Class="Triggero.MauiMobileApp.Views.Popups.YouKassaPopup">
    <toolkit:Popup.Content>
        <Frame
            BackgroundColor="White"
            Background="White"
            IsClippedToBounds="True"
            HasShadow="False"
            CornerRadius="15"
            Padding="0"
            Margin="0,187,0,-100"
            VerticalOptions="Fill">
            <Grid x:Name="grid">

                <!--<WebView x:Name="webView" />-->

                <Grid x:Name="webViewGrid">
                    
                </Grid>

                <ImageButton 
                    BackgroundColor="Transparent"
                    Command="{Binding Source={x:Reference this},Path=Close}"
                    CornerRadius="0"
                    Margin="0,10,10,0"
                    HorizontalOptions="End"
                    VerticalOptions="Start"
                    WidthRequest="14"
                    HeightRequest="14"
                    Source="close.png"/>
            </Grid>
        </Frame>
    </toolkit:Popup.Content>
</toolkit:Popup>