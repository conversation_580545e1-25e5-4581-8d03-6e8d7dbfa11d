﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices.ComTypes;
using System.Text;
using System.Threading.Tasks;
using Triggero.Custom.ScrollableCalendarItem;



namespace Triggero.Custom.ScrollableCalendarItems
{
    
    public partial class CalendarMonthView : ContentView
    {
        public int Year { get; private set; }
        public int Month { get; private set; }

        private ScrollableCalendarV2 _parent;
        public CalendarMonthView(int year,int month, ScrollableCalendarV2 parent)
        {
            InitializeComponent();

            Year = year;
            Month = month;

            _parent = parent;
            Render(year,month);
        }

        private void Render(int year, int month)
        {
            var startMonthDate = new DateTime(year, month, 1);
            var date = startMonthDate;

            this.HorizontalOptions = LayoutOptions.Center;

            var label = new Label
            {
                Text = startMonthDate.ToString("MMMM yyyy"),
                HorizontalOptions = LayoutOptions.Center,
                FontSize = 16,
                TextColor = Colors.Black,
                FontAttributes = FontAttributes.Bold
            };
            calendarLayout.Children.Add(label);

            while (date.Month == month)
            {
                var rowData = MakeCalendarWeekRow(date);
                date = rowData.DateToContinue;

                calendarLayout.Children.Add(rowData.View);
            }
        }



        private CalendarWeekRowData MakeCalendarWeekRow(DateTime startDate)
        {
            var row = new CalendarWeekRow(startDate, _parent);

            return new CalendarWeekRowData
            {
                View = row,
                DateToContinue = row.DateEnd,
            };
        }
    }
}