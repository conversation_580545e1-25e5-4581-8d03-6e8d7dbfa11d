﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.Pages.Profile.Notifications.SetNotificationDatePage"
             xmlns:app="clr-namespace:Triggero" 
             xmlns:syncfusion="clr-namespace:Syncfusion.Maui.Picker;assembly=Syncfusion.Maui.Picker"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <ContentPage.Content>
        <Grid>

            <Image
                Aspect="Fill"
                Source="notificationsBlur.png"/>


            <Frame
                CornerRadius="20"
                HeightRequest="320"
                Margin="0,0,0,-20"
                Padding="0"
                VerticalOptions="End"
                HorizontalOptions="Fill"
                BackgroundColor="White"
                HasShadow="False">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="42"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0"
                          Margin="20,0,20,0">


                        <Label 
                            TextColor="{x:StaticResource yellowColor}"
                            FontSize="{OnPlatform Android=14,iOS=17}"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            Margin="0,0,0,0"
                            FontAttributes="Bold"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileSelectTime.Cancel}">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=Close}"/>
                            </Label.GestureRecognizers>
                        </Label>


                        <Label 
                            TextColor="#000000"
                            FontSize="{OnPlatform Android=14,iOS=17}"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            Margin="0,0,0,0"
                            FontAttributes="Bold"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileSelectTime.Replace}">
                        </Label>




                        <Label 
                            TextColor="{x:StaticResource yellowColor}"
                            FontSize="{OnPlatform Android=14,iOS=17}"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="End"
                            Margin="0,0,0,0"
                            FontAttributes="Bold"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileSelectTime.Save}">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=SelectDate}"/>
                            </Label.GestureRecognizers>
                        </Label>

                    </Grid>

                    <Grid Grid.Row="1">

                        <StackLayout 
                            Spacing="0"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"
                            Orientation="Horizontal">

                            <syncfusion:SfPicker
                                x:Name="hoursSfPicker"
                                Margin="0,-20,0,0"
                                WidthRequest="70"
                                HeightRequest="200"
                                Mode="Default"
                                EnableLooping="True"
                                HorizontalOptions="Center"
                                VerticalOptions="Fill">
                                <!-- TODO: Properties like ColumnHeaderBackgroundColor, HeaderBackgroundColor, BorderColor, ShowHeader are not supported in MAUI -->
                                <!-- TODO: UnSelectedItemFontSize, SelectedItemTextColor, UnSelectedItemTextColor, SelectedItemFontSize need to be set via TextStyle and SelectedTextStyle -->
                                <syncfusion:SfPicker.Columns>
                                    <syncfusion:PickerColumn ItemsSource="{Binding Source={x:Reference this},Path=Hours}" />
                                </syncfusion:SfPicker.Columns>
                            </syncfusion:SfPicker>


                       




                            <syncfusion:SfPicker
                                Margin="0,-20,0,0"
                                x:Name="minutesSfPicker"
                                WidthRequest="70"
                                HeightRequest="200"
                                Mode="Default"
                                EnableLooping="True"
                                HorizontalOptions="Center"
                                VerticalOptions="Fill">
                                <!-- TODO: Properties like ColumnHeaderBackgroundColor, HeaderBackgroundColor, BorderColor, ShowHeader are not supported in MAUI -->
                                <!-- TODO: UnSelectedItemFontSize, SelectedItemTextColor, UnSelectedItemTextColor, SelectedItemFontSize need to be set via TextStyle and SelectedTextStyle -->
                                <syncfusion:SfPicker.Columns>
                                    <syncfusion:PickerColumn ItemsSource="{Binding Source={x:Reference this},Path=Minutes}" />
                                </syncfusion:SfPicker.Columns>
                            </syncfusion:SfPicker>

                           

                        </StackLayout>
                        
                        
                    </Grid>

                </Grid>
            </Frame>

        </Grid>
    </ContentPage.Content>
</ContentPage>