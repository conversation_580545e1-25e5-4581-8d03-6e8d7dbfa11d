﻿
using CommunityToolkit.Maui.Views;
using System.Windows.Input;
using Triggero.Models.MoodTracker.User;
using Triggero.MauiMobileApp.Views.Pages.MoodTracker;
using Triggero.MauiMobileApp.Extensions.Helpers;

namespace Triggero.MauiMobileApp.Views.Popups
{

    public partial class TrackerStartPopup : Popup
    {
        public TrackerStartPopup()
        {
            InitializeComponent();
            Tracker = new MoodtrackerItem();
            Tracker.Note = new MoodtrackerNote
            {
                UserId = AuthHelper.UserId
            };




        }



        private MoodtrackerItem tracker = new MoodtrackerItem();
        public MoodtrackerItem Tracker
        {
            get { return tracker; }
            set { tracker = value; OnPropertyChanged(nameof(Tracker)); }
        }




        private ICommand setMood;
        public ICommand SetMood
        {
            get => setMood ??= new RelayCommand(async obj =>
            {
                int mood = Convert.ToInt32(obj);
                Tracker.Mood = mood;

                //await App.Current.MainPage.Navigation.PopAsync(false);
                App.OpenPage(new TrackerHowAreYou(Tracker));
            });
        }
        private ICommand goToTracker;
        public ICommand GoToTracker
        {
            get => goToTracker ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new TrackerMainPage());
                await this.CloseAsync();
            });
        }
        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await this.CloseAsync();
            });
        }
    }
}